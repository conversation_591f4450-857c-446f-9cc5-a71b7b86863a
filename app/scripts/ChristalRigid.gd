extends RigidBody2D

var crystalType = Global.CrystalType.c5
var crystalValue = 5

var wasInit = false

func _on_hit(area):
	if area.has_method("canGetPowerup"):
		apply(area)

func apply(_area):

	if not wasInit:
		return false

	# don't pick up if player is dead or just entering the level
	if not Global.GameScene.isPlayerReady():
		return false

	Global.playSound(SoundManager.MoneySound,Global.getPlayerPosition(),-5,(randf()/2)+1.0)
	Global.GameScene.money+=crystalValue
	Global.GameScene.FlashWorldEnv(0.1)
	Global.GameScene.shakeCamera(0.02)

	Global.GameScene.spawnBonusLabel(position ,"$"+str(crystalValue),2,true,false,0.8,0,randi()%50,true);

	queue_free()

func _ready():
	self.visible = false
	var _c = $"Area2D".connect("area_entered",self,"_on_hit")

func init(cType, _doSpreadMore = false):

	if(_doSpreadMore):
		applyImpulseTimes = 3

	crystalType = cType
	crystalValue = Global.crystals[crystalType]["value"]

	# ship spec multipier
	crystalValue = ceil(crystalValue*ShipSpecs.getSpecs().crystal_value_multiplier)

	$AnimatedSprite.animation = Global.crystals[crystalType]["animation"]
	self.visible = true

	self.gravity_scale = 1+randf()*0.5;

	wasInit = true
	
var physicsCnt = 0
var applyImpulseTimes = 1

func doPushToPlayer():
	return Global.GameScene.hasPlayerEffect(Global.PlayerEffect.CRYSTAL_MAGNET) || ShipSpecs.getSpecs().permanent_crystal_magnet

var push_power = randi()%20+50

func _physics_process(_delta):
	if(physicsCnt<applyImpulseTimes):
		self.apply_impulse(Vector2(randf()*16,randf()*16), Vector2(randf()*90-45, -(randf()*50+20)))
		physicsCnt+=1
	
	var distancex = Global.getPlayerPosition().x - self.global_position.x
	var distancey = Global.getPlayerPosition().y - self.global_position.y

	# push toward if have effect
	if(doPushToPlayer() && abs(distancex)<200 && abs(distancey)<200):

		if(distancey<=5):
			distancey = 5

		self.global_position.x = self.global_position.x + (distancex * (push_power/distancey) * _delta)

		# var direction = 1;
		# if(distancex<0):
		# 	direction = -1
		# self.global_position.x = self.global_position.x + (direction * push_power * _delta)

func _process(_delta):

	if not wasInit:
		return false

	# check if out of screen
	if Global.isOffScreenBottom(position, 200):
		queue_free()


