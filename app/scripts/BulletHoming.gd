extends Area2D

var Sparkle = preload("res://scenes/Sparkle.tscn")

# we can set this to true to make the bullet pass through enemies
export var isPassThrough = false

var Speed = Config.BulletSpeed * 0.7

var isDestroyed = false

var canClash = false
var doCountTowardBulletsOnScreen = true
var wasBulletRemoved = false

func decreaseBulletCnt():
	if(doCountTowardBulletsOnScreen && !wasBulletRemoved):
		wasBulletRemoved = true
		Global.GameScene.BulletsOnScreen -= 1
		Global.GameScene.BulletsOnScreen = max(0, Global.GameScene.BulletsOnScreen)

func destroy(force = false):

	if isDestroyed:
		return false
	
	isDestroyed = true

	# if we don't force the bullet to be destroyed, we only destroy it if it's not a passthrough bullet
	if(not force):
		if(isPassThrough):
			return false
	
	decreaseBulletCnt()

	var sparkle = Sparkle.instance()
	sparkle.position = position
	Global.GameScene.add_child(sparkle)

	queue_free()

func getDamagePoint():
	if(Global.GameScene.isBulletOP):
		return 1000
	return 10*ShipSpecs.getSpecs().weapon_strength_multiplier

func getSpeedMod():
	return 1.0

func setSpeed(speed):
	Speed = speed

var steer_force = 25.0 * ShipSpecs.getSpecs().homing_steer_force_multiplier

var velocity = Vector2.ZERO
var acceleration = Vector2.ZERO
var target = null

var steerTimer = 0
var destroyTimer = 0

var lifetimeMsec = 2000
var seekStartMsec = 200

func checkDestroy():
	if isDestroyed:
		return

	if Tick.ms() > destroyTimer:
		# don't follow after destroyTime
		$LightParticle.visible = false
		steer_force = 0
		# self.destroy()
	
	if Global.isOffScreen(global_position,50):
		Global.GameScene.levelConductor.logBullet(true)
		self.destroy()

func canSteer():
	return Tick.ms() > steerTimer

func _ready():
	Global.GameScene.levelConductor.logBullet()

func start(_target = null):

	steer_force = steer_force+(randi()%15)

	steerTimer = Tick.ms() + seekStartMsec
	destroyTimer = Tick.ms() + lifetimeMsec

	global_transform = Transform2D()
	rotation_degrees = -90
	velocity = transform.x * Speed
	target = _target

func selectTarget():

	if(Global.doThrottle("HomingBullet"+str(get_instance_id()),200)):
		return
	
	if is_instance_valid(self.target):
		return
	
	var newTarget = Global.GameScene.getLevelObject().getARandomTarget(true)

	if !newTarget:
		return
	
	self.target = newTarget
	

func seek():
	var steer = Vector2.ZERO

	if is_instance_valid(target) and canSteer():
		var desired = (target.global_position - position).normalized() * Speed
		steer = (desired - velocity).normalized() * steer_force

	return steer

var isTimerActive = false

func _process(_delta):
	# remove bullet count after some time sec
	if(!isTimerActive):
		Global.setTimeout(self,1.2,self,"decreaseBulletCnt")
		isTimerActive = true

	checkDestroy()
	selectTarget()

func _physics_process(delta):
	acceleration += seek()
	velocity += acceleration * delta
	velocity = velocity.clamped(Speed)
	rotation = velocity.angle()
	position += velocity * delta
