[gd_scene load_steps=62 format=2]

[ext_resource path="res://scripts/ChristalRigid.gd" type="Script" id=1]
[ext_resource path="res://assets/crystal-qubodup-ccby3-16-green.png" type="Texture" id=2]
[ext_resource path="res://assets/crystal-qubodup-ccby3-16-blue.png" type="Texture" id=3]
[ext_resource path="res://assets/crystal-qubodup-ccby3-16-grey.png" type="Texture" id=4]
[ext_resource path="res://assets/crystal-qubodup-ccby3-16-pink.png" type="Texture" id=5]
[ext_resource path="res://assets/crystal-qubodup-ccby3-16-orange.png" type="Texture" id=6]
[ext_resource path="res://assets/crystal-qubodup-ccby3-16-yellow.png" type="Texture" id=7]

[sub_resource type="Animation" id=19]
resource_name = "ChristalAnimation"
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("AnimatedSprite:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5, 1 ),
"transitions": PoolRealArray( 0.5, 2, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 1.2, 1.2 ), Vector2( 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("AnimatedSprite:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.2, 0.8, 1 ),
"transitions": PoolRealArray( 2, 0.5, 2, 1 ),
"update": 0,
"values": [ 0.0, 10.0, -10.0, 0.0 ]
}

[sub_resource type="CapsuleShape2D" id=18]
height = 4.0

[sub_resource type="AtlasTexture" id=21]
atlas = ExtResource( 3 )
region = Rect2( 0, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=22]
atlas = ExtResource( 3 )
region = Rect2( 16, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=23]
atlas = ExtResource( 3 )
region = Rect2( 32, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=24]
atlas = ExtResource( 3 )
region = Rect2( 48, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=25]
atlas = ExtResource( 3 )
region = Rect2( 64, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=26]
atlas = ExtResource( 3 )
region = Rect2( 80, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=27]
atlas = ExtResource( 3 )
region = Rect2( 96, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=28]
atlas = ExtResource( 3 )
region = Rect2( 112, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=29]
atlas = ExtResource( 5 )
region = Rect2( 0, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=30]
atlas = ExtResource( 5 )
region = Rect2( 16, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=31]
atlas = ExtResource( 5 )
region = Rect2( 32, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=32]
atlas = ExtResource( 5 )
region = Rect2( 48, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=33]
atlas = ExtResource( 5 )
region = Rect2( 64, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=34]
atlas = ExtResource( 5 )
region = Rect2( 80, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=35]
atlas = ExtResource( 5 )
region = Rect2( 96, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=36]
atlas = ExtResource( 5 )
region = Rect2( 112, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=37]
atlas = ExtResource( 4 )
region = Rect2( 0, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=38]
atlas = ExtResource( 4 )
region = Rect2( 16, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=39]
atlas = ExtResource( 4 )
region = Rect2( 32, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=40]
atlas = ExtResource( 4 )
region = Rect2( 48, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=41]
atlas = ExtResource( 4 )
region = Rect2( 64, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=42]
atlas = ExtResource( 4 )
region = Rect2( 80, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=43]
atlas = ExtResource( 4 )
region = Rect2( 96, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=44]
atlas = ExtResource( 4 )
region = Rect2( 112, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=45]
atlas = ExtResource( 7 )
region = Rect2( 0, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=46]
atlas = ExtResource( 7 )
region = Rect2( 16, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=47]
atlas = ExtResource( 7 )
region = Rect2( 32, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=48]
atlas = ExtResource( 7 )
region = Rect2( 48, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=49]
atlas = ExtResource( 7 )
region = Rect2( 64, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=50]
atlas = ExtResource( 7 )
region = Rect2( 80, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=51]
atlas = ExtResource( 7 )
region = Rect2( 96, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=52]
atlas = ExtResource( 7 )
region = Rect2( 112, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=10]
atlas = ExtResource( 2 )
region = Rect2( 0, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=11]
atlas = ExtResource( 2 )
region = Rect2( 16, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=12]
atlas = ExtResource( 2 )
region = Rect2( 32, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=13]
atlas = ExtResource( 2 )
region = Rect2( 48, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=14]
atlas = ExtResource( 2 )
region = Rect2( 64, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=15]
atlas = ExtResource( 2 )
region = Rect2( 80, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=16]
atlas = ExtResource( 2 )
region = Rect2( 96, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=17]
atlas = ExtResource( 2 )
region = Rect2( 112, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=53]
atlas = ExtResource( 6 )
region = Rect2( 0, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=54]
atlas = ExtResource( 6 )
region = Rect2( 16, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=55]
atlas = ExtResource( 6 )
region = Rect2( 32, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=56]
atlas = ExtResource( 6 )
region = Rect2( 48, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=57]
atlas = ExtResource( 6 )
region = Rect2( 64, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=58]
atlas = ExtResource( 6 )
region = Rect2( 80, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=59]
atlas = ExtResource( 6 )
region = Rect2( 96, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=60]
atlas = ExtResource( 6 )
region = Rect2( 112, 0, 16, 16 )

[sub_resource type="SpriteFrames" id=9]
animations = [ {
"frames": [ SubResource( 21 ), SubResource( 22 ), SubResource( 23 ), SubResource( 24 ), SubResource( 25 ), SubResource( 26 ), SubResource( 27 ), SubResource( 28 ) ],
"loop": true,
"name": "c10",
"speed": 10.0
}, {
"frames": [ SubResource( 29 ), SubResource( 30 ), SubResource( 31 ), SubResource( 32 ), SubResource( 33 ), SubResource( 34 ), SubResource( 35 ), SubResource( 36 ) ],
"loop": true,
"name": "c100",
"speed": 10.0
}, {
"frames": [ SubResource( 37 ), SubResource( 38 ), SubResource( 39 ), SubResource( 40 ), SubResource( 41 ), SubResource( 42 ), SubResource( 43 ), SubResource( 44 ) ],
"loop": true,
"name": "c20",
"speed": 10.0
}, {
"frames": [ SubResource( 45 ), SubResource( 46 ), SubResource( 47 ), SubResource( 48 ), SubResource( 49 ), SubResource( 50 ), SubResource( 51 ), SubResource( 52 ) ],
"loop": true,
"name": "c200",
"speed": 10.0
}, {
"frames": [ SubResource( 10 ), SubResource( 11 ), SubResource( 12 ), SubResource( 13 ), SubResource( 14 ), SubResource( 15 ), SubResource( 16 ), SubResource( 17 ) ],
"loop": true,
"name": "c5",
"speed": 10.0
}, {
"frames": [ SubResource( 53 ), SubResource( 54 ), SubResource( 55 ), SubResource( 56 ), SubResource( 57 ), SubResource( 58 ), SubResource( 59 ), SubResource( 60 ) ],
"loop": true,
"name": "c50",
"speed": 10.0
} ]

[sub_resource type="Gradient" id=61]
colors = PoolColorArray( 1, 1, 1, 0.431373, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=62]
gradient = SubResource( 61 )

[sub_resource type="ParticlesMaterial" id=20]
emission_shape = 1
emission_sphere_radius = 8.0
flag_disable_z = true
direction = Vector3( 0, -1, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 20.0
initial_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
radial_accel = 1.0
radial_accel_random = 1.0
scale_random = 1.0
color_ramp = SubResource( 62 )
hue_variation = 1.0
hue_variation_random = 0.47

[node name="Crystal" type="RigidBody2D"]
script = ExtResource( 1 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
autoplay = "ChristalAnimation"
anims/ChristalAnimation = SubResource( 19 )

[node name="CollisionShape2D_rigidbody" type="CollisionShape2D" parent="."]
visible = false
shape = SubResource( 18 )
disabled = true

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
rotation = -0.0536082
scale = Vector2( 1.19774, 1.19774 )
frames = SubResource( 9 )
animation = "c200"
frame = 7
playing = true

[node name="Particles2D" type="Particles2D" parent="."]
modulate = Color( 1, 1, 1, 0.490196 )
amount = 20
lifetime = 2.0
speed_scale = 2.0
randomness = 1.0
local_coords = false
process_material = SubResource( 20 )

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D_area" type="CollisionShape2D" parent="Area2D"]
visible = false
shape = SubResource( 18 )
